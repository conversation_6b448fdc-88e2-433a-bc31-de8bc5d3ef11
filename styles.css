/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.contract-address {
    background-color: #111;
    padding: 15px;
    margin: 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: monospace;
    font-size: 1.1rem;
    color: #ffd700;
    overflow: hidden;
    border-radius: 4px;
    gap: 10px;
}

.copy-btn {
    background-color: #ff6b00;
    color: #000;
    border: none;
    font-family: 'VT323', monospace;
    font-size: 1.2rem;
    padding: 8px 15px;
    cursor: pointer;
    border-radius: 4px;
}

.copy-btn:hover {
    background-color: #ffd700;
}

body {
    font-family: 'VT323', monospace;
    background-color: #000;
    color: #fff;
    background-image: url('https://i.imgur.com/ZFytqPv.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.quiz-container {
    background-color: rgba(34, 34, 34, 0.95);
    border: 4px solid #ffd700;
    width: 100%;
    max-width: 800px;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    position: relative;
    overflow: hidden;
}

/* Pixelated text effects */
.glitch-text {
    font-size: 3rem;
    text-align: center;
    color: #ffd700;
    text-shadow: 3px 3px 0 #ff6b00;
    margin-bottom: 20px;
    letter-spacing: 2px;
    animation: glitch 1s infinite alternate;
}

@keyframes glitch {
    0% {
        text-shadow: 3px 3px 0 #ff6b00;
    }
    25% {
        text-shadow: -3px -3px 0 #ff6b00;
    }
    50% {
        text-shadow: 3px -3px 0 #ff6b00;
    }
    75% {
        text-shadow: -3px 3px 0 #ff6b00;
    }
    100% {
        text-shadow: 3px 3px 0 #ff6b00;
    }
}

.subtitle {
    font-size: 1.8rem;
    text-align: center;
    color: #ff6b00;
    margin-bottom: 15px;
}

.description {
    font-size: 1.4rem;
    text-align: center;
    color: #ccc;
    margin-bottom: 30px;
}

/* Sections */
.section {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.section.active {
    display: block;
    opacity: 1;
}

/* Buttons */
.pixel-btn {
    background-color: #ff6b00;
    color: #000;
    border: none;
    font-family: 'VT323', monospace;
    font-size: 1.5rem;
    padding: 12px 24px;
    cursor: pointer;
    display: block;
    margin: 20px auto;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
    transition: background-color 0.3s;
}

.pixel-btn:hover {
    background-color: #ffd700;
}

.pixel-btn.secondary {
    background-color: #444;
    color: #fff;
}

.pixel-btn.secondary:hover {
    background-color: #666;
}

/* Quiz styles */
.progress-bar {
    height: 20px;
    width: 100%;
    background-color: #444;
    margin-bottom: 20px;
    position: relative;
}

.progress {
    height: 100%;
    width: 0%;
    background-color: #ffd700;
    transition: width 0.3s;
}

#question-container {
    font-size: 1.4rem;
    margin-bottom: 20px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.3);
    border-left: 4px solid #ffd700;
}

.options-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    margin-bottom: 20px;
}

.navigation-buttons {
    display: flex;
    justify-content: flex-start;
    margin-top: 20px;
}

.option {
    background-color: #333;
    padding: 12px;
    border: 2px solid #666;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.2s;
}

.option:hover {
    background-color: #444;
    border-color: #ffd700;
}

.option.selected {
    background-color: #ffd700;
    color: #000;
    border-color: #ff6b00;
}

/* Results styles */
.result-header {
    font-size: 2rem;
    text-align: center;
    color: #ff6b00;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.degen-level {
    font-size: 2.5rem;
    text-align: center;
    color: #ffd700;
    margin: 20px 0;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px dashed #ffd700;
}

.degen-description {
    font-size: 1.4rem;
    text-align: center;
    color: #ccc;
    margin-bottom: 30px;
    padding: 10px;
}

/* Dropdown styles */
.dropdown-container {
    margin: 20px 0;
    position: relative;
}

.dropdown-content {
    display: none;
    margin: 15px 0;
    padding: 20px;
    background-color: rgba(34, 34, 34, 0.95);
    font-size: 1.2rem;
    border-radius: 8px;
    font-family: monospace;
    border: 2px solid #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.4);
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.dropdown-content.show {
    display: block;
}

#score-dropdown-btn {
    background-color: #ffd700;
    color: #000;
    border: 2px solid #ff6b00;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    font-size: 1.3rem;
    transition: all 0.3s ease;
}

#score-dropdown-btn:hover {
    background-color: #ff6b00;
    border-color: #ffd700;
    transform: translateY(-2px);
}

#score-breakdown {
    margin: 0;
    padding: 0;
}

.final-score {
    font-size: 1.8rem;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-bottom: 1px solid #ff6b00;
}

.score-list {
    list-style-type: none;
    padding-left: 0;
    color: #ccc;
    line-height: 1.8;
}

.score-list li {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.2);
    border-left: 3px solid #444;
    transition: all 0.3s ease;
}

.score-list li:hover {
    background-color: rgba(0, 0, 0, 0.4);
    border-left-color: #ff6b00;
}

.score-list .answer {
    color: #ff6b00;
    font-weight: bold;
    display: block;
    margin-top: 5px;
}

.score-list .points {
    color: #ffd700;
    display: inline-block;
    margin-top: 5px;
    float: right;
    padding: 2px 8px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
}

.pixel-input {
    width: 100%;
    background-color: #111;
    border: 2px solid #444;
    color: #fff;
    font-family: 'VT323', monospace;
    padding: 12px;
    font-size: 1.2rem;
    transition: border-color 0.3s;
}

.pixel-input:focus {
    border-color: #ffd700;
    outline: none;
}

.degen-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
    margin-top: 30px;
}

.degen-links a {
    flex-basis: calc(50% - 10px);
    text-decoration: none;
    text-align: center;
    padding: 15px 20px;
    border-radius: 10px;
    font-size: 1.4rem;
    font-weight: bold;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
}

.game-btn {
    background-color: #2d0e12;
    color: #ffd700;
    border: 2px solid #ff6b00;
    box-shadow: 0 0 10px rgba(255, 107, 0, 0.5);
}

.game-btn:hover {
    background-color: #3d1b1d;
    box-shadow: 0 0 15px rgba(255, 107, 0, 0.8);
}

.pfp-btn {
    background-color: #0e2d1c;
    color: #ffd700;
    border: 2px solid #00ff73;
    box-shadow: 0 0 10px rgba(0, 255, 115, 0.5);
}

.pfp-btn:hover {
    background-color: #1b3d29;
    box-shadow: 0 0 15px rgba(0, 255, 115, 0.8);
}

.degen-links .confess-btn {
    background-color: #1c0e2d;
    color: #ffd700;
    border: 2px solid #c400ff;
    box-shadow: 0 0 10px rgba(196, 0, 255, 0.5);
    flex-basis: 100% !important;
    width: 100%;
    margin-top: 5px;
}

.degen-links .confess-btn:hover {
    background-color: #291b3d;
    box-shadow: 0 0 15px rgba(196, 0, 255, 0.8);
}

/* Responsive design */
@media (max-width: 768px) {
    .quiz-container {
        padding: 20px;
    }
    
    .glitch-text {
        font-size: 2.5rem;
    }
    
    .subtitle {
        font-size: 1.5rem;
    }
    
    .degen-links a {
        flex-basis: 100%;
    }
}